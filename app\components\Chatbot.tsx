"use client";

import { useState, useRef, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import ReactMarkdown from "react-markdown";
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import { atomDark } from "react-syntax-highlighter/dist/esm/styles/prism";
import { Send, Loader2 } from "lucide-react";

interface Message {
  role: "user" | "assistant";
  content: string;
}

export function Chatbot() {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    // Use page-level scrolling instead of internal scroll
    window.scrollTo({ top: document.body.scrollHeight, behavior: "smooth" });
  };

  useEffect(scrollToBottom, []); // Updated useEffect dependency

  const handleSendMessage = async () => {
    if (!input.trim() || isLoading) return;

    console.log("Bắt đầu gửi tin nhắn, input:", input);

    const userMessage: Message = { role: "user", content: input };
    const newMessages = [...messages, userMessage];
    setMessages(newMessages);
    const currentInput = input; // Store input before clearing
    setInput("");
    setIsLoading(true);
    // Removed scrollToBottom() here, will scroll after response

    // Removed placeholder message addition

    try {
      const { getApiUrl } = await import("@/lib/config");
      const apiUrl = getApiUrl("docsRagAgent");
      console.log("Gọi API đến:", apiUrl, "với dữ liệu:", {
        text: currentInput,
        session: "default_session",
      });

      const { apiFetch } = await import("@/lib/config");
      const response = await apiFetch("docsRagAgent", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          text: currentInput, // Use stored input
          session: "default_session",
        }),
      });

      let assistantMessage: Message;
      if (response.ok) {
        const data = await response.json();
        assistantMessage = { role: "assistant", content: data.output };
      } else {
        console.error("API Error:", response.status, await response.text()); // Log error details
        assistantMessage = {
          role: "assistant",
          content: `Xin lỗi, tôi gặp lỗi (${response.status}) khi xử lý yêu cầu của bạn. Vui lòng thử lại.`,
        };
      }
      setMessages([...newMessages, assistantMessage]);
    } catch (error) {
      console.error("Lỗi khi gửi tin nhắn:", error);
      const errorMessage: Message = {
        role: "assistant",
        content: "Xin lỗi, đã xảy ra lỗi kết nối. Vui lòng thử lại.",
      };
      if (error instanceof Error) {
        errorMessage.content += ` Chi tiết: ${error.message}`;
      }
      setMessages([...newMessages, errorMessage]);
    } finally {
      setIsLoading(false);
      // Scroll to bottom after messages state is updated and component re-renders
      // Use a slight delay to ensure DOM update before scrolling
      setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
      }, 0);
    }
  };

  // Update useEffect to scroll when messages change - use page scroll
  useEffect(() => {
    // Use a slight delay to ensure DOM update before scrolling
    setTimeout(() => {
      window.scrollTo({ top: document.body.scrollHeight, behavior: "smooth" });
    }, 100);
  }, [messages]);

  return (
    <div className="w-full max-w-4xl mx-auto bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl shadow-xl border-0 mb-20">
      <div className="p-6">
        {messages.length === 0 && (
          <div className="text-center text-gray-500 mt-8">
            <div className="mb-4">
              <span className="text-4xl">💬</span>
            </div>
            <p className="text-lg font-medium">Chào mừng bạn đến với Chatbot MTTQ</p>
            <p className="text-sm mt-2">Hãy bắt đầu cuộc trò chuyện bằng cách đặt câu hỏi</p>
          </div>
        )}
        {messages.map((message, index) => (
          <div
            key={index}
            className={`mb-6 ${
              message.role === "user" ? "text-right" : "text-left"
            }`}
          >
            <div
              className={`inline-block max-w-[85%] p-4 rounded-2xl shadow-sm ${
                message.role === "user"
                  ? "bg-gradient-to-r from-blue-500 to-indigo-500 text-white"
                  : "bg-white text-gray-800 border border-gray-100"
              }`}
            >
              <ReactMarkdown
                components={{
                  code({ className, children, ...props }: any) {
                    const match = /language-(\w+)/.exec(className || "");
                    return match ? (
                      <SyntaxHighlighter
                        style={atomDark}
                        language={match[1]}
                        PreTag="div"
                        {...props}
                      >
                        {String(children).replace(/\n$/, "")}
                      </SyntaxHighlighter>
                    ) : (
                      <code className={`${className} bg-gray-100 px-2 py-1 rounded text-sm`} {...props}>
                        {children}
                      </code>
                    );
                  },
                }}
              >
                {message.content}
              </ReactMarkdown>
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>
      
      {/* Fixed input at bottom of page */}
      <div className="fixed bottom-0 left-0 right-0 bg-white shadow-lg border-t border-gray-200 p-4 z-10">
        <div className="max-w-4xl mx-auto flex items-center space-x-3 bg-gray-50 p-4 rounded-2xl shadow-sm border border-gray-100">
          <Input
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyPress={(e) => e.key === "Enter" && handleSendMessage()}
            placeholder="Nhập câu hỏi về Đoàn - Hội..."
            disabled={isLoading}
            className="flex-1 border-0 focus:ring-2 focus:ring-blue-500 focus:ring-offset-0 bg-white rounded-xl px-4 py-3 text-gray-800 placeholder-gray-500"
          />
          <Button
            onClick={handleSendMessage}
            disabled={isLoading}
            className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 rounded-xl px-6 py-3 transition-all duration-200 shadow-md hover:shadow-lg"
          >
            {isLoading ? (
              <Loader2 className="w-5 h-5 animate-spin" />
            ) : (
              <Send className="w-5 h-5" />
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}
