import { newsItems, NewsItem } from "@/data/news";
import { Header } from "@/components/shared/header";
import { notFound } from 'next/navigation';
import Link from 'next/link'; // Import Link for navigation
import { Chatbot } from "@/components/Chatbot";

interface NewsDetailPageProps {
  params: {
    id: string;
  };
}

export async function generateStaticParams() {
  return newsItems.map((item) => ({
    id: item.id.toString(),
  }));
}

export default function NewsDetailPage({ params }: NewsDetailPageProps) {
  const { id } = params;
  const newsId = parseInt(id, 10);

  if (isNaN(newsId)) {
    notFound();
  }

  const selectedNewsItem = newsItems.find((item) => item.id === newsId);

  if (!selectedNewsItem) {
    notFound();
  }

  const { title, date, image, content, category } = selectedNewsItem;

  // Get suggested articles (e.g., 4 other articles, excluding the current one)
  const suggestedArticles = newsItems
    .filter((item) => item.id !== newsId)
    .slice(0, 4);

  return (
    <>
      <Header />
      <div className="bg-gray-50 py-6 md:py-8">
        <div className="container mx-auto px-3 sm:px-4 lg:px-6">
          <div className="flex flex-col lg:flex-row lg:gap-4 xl:gap-6">
            {/* Column 1: Chatbot (Left) */}
            <div className="lg:w-[28%] order-2 lg:order-1 mt-6 lg:mt-0">
              <div className="lg:sticky lg:top-24">
                <div className="bg-white rounded-lg shadow-sm p-3">
                  <div className="mb-3">
                    <h2 className="text-lg font-medium text-gray-800">Chat với AI</h2>
                  </div>
                  <div className="h-[calc(100vh-14rem)] max-h-[600px] lg:h-auto lg:max-h-[calc(100vh-12rem)] overflow-y-auto">
                    <div className="pr-2">
                      <Chatbot initialContext={content} chatType="ask-in-content" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Column 2: Main Article Content (Center) */}
            <div className="lg:w-[52%] order-1 lg:order-2">
              <article className="bg-white p-5 sm:p-6 md:p-8 rounded-lg shadow-xl border border-gray-200">
                <div className="mb-5 md:mb-6">
                  <img
                    src={image}
                    alt={title}
                    className="w-full h-auto max-h-[500px] object-cover rounded-lg shadow-md"
                  />
                </div>
                <header className="mb-6">
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between text-sm text-gray-500 mb-3">
                    <span className="inline-block bg-indigo-100 text-indigo-700 px-3 py-1.5 rounded-full font-semibold text-xs uppercase tracking-wider self-start sm:self-center">
                      {category}
                    </span>
                    <span className="mt-2 sm:mt-0">{date}</span>
                  </div>
                  <h1 className="text-3xl sm:text-4xl font-extrabold text-gray-900 leading-tight">
                    {title}
                  </h1>
                </header>
                <div
                  className="prose prose-lg sm:prose-xl max-w-none text-gray-700 leading-relaxed"
                  dangerouslySetInnerHTML={{ __html: content }}
                />
              </article>
            </div>

            {/* Column 3: Suggested News (Right) */}
            <div className="lg:w-[20%] order-3 lg:order-3 mt-6 lg:mt-0">
              <div className="lg:sticky lg:top-24 space-y-4">
                <div className="bg-white rounded-lg shadow-xl p-3 border border-gray-200">
                  <h2 className="text-lg font-semibold text-gray-800 pb-2 border-b border-gray-200">
                    Tin tức khác
                  </h2>
                  {suggestedArticles.length > 0 ? (
                    <ul className="space-y-3 mt-3">
                      {suggestedArticles.map((item) => (
                        <li key={item.id} className="bg-gray-50 p-2 rounded-lg hover:shadow-md transition-shadow duration-200">
                          <Link href={`/news/${item.id}`} className="group">
                            <div className="flex space-x-2 items-start">
                              {item.image && (
                                <div className="flex-shrink-0 w-14 h-14">
                                  <img 
                                    src={item.image} 
                                    alt={item.title} 
                                    className="w-full h-full object-cover rounded-md"
                                  />
                                </div>
                              )}
                              <div className="flex-1 min-w-0">
                                <span className="text-xs font-semibold text-indigo-600 bg-indigo-50 px-2 py-0.5 rounded-full mb-1 inline-block">
                                  {item.category}
                                </span>
                                <h3 className="text-xs font-semibold text-gray-700 group-hover:text-indigo-600 transition-colors duration-200 line-clamp-2">
                                  {item.title}
                                </h3>
                                <p className="text-[10px] text-gray-500 mt-1">{item.date}</p>
                              </div>
                            </div>
                          </Link>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-gray-500 mt-3">Không có tin tức nào khác.</p>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
} 