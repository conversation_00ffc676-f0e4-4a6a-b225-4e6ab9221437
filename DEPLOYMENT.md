# Hướng dẫn Deployment Thai Binh Chatbot

## <PERSON><PERSON><PERSON> c<PERSON>u hệ thống

- Docker và Docker Compose được cài đặt
- Port 80 và 443 available
- Ít nhất 2GB RAM và 10GB disk space

## Quick Start

### 1. Tạo SSL certificate (tùy chọn)

**Cho development/testing:**
```bash
# Windows
docker-manage.bat ssl

# Linux/Mac
./docker-manage.sh ssl
```

**Cho production với domain thật:**
```bash
# Sử dụng Let's Encrypt
certbot certonly --standalone -d yourdomain.com
mkdir ssl
cp /etc/letsencrypt/live/yourdomain.com/fullchain.pem ssl/cert.pem
cp /etc/letsencrypt/live/yourdomain.com/privkey.pem ssl/key.pem
```

### 2. Chạy ứng dụng

**Với SSL:**
```bash
# Windows
docker-manage.bat start

# Linux/Mac
./docker-manage.sh start

# Hoặc manual
docker-compose up -d --build
```

**Không SSL (chỉ HTTP):**
```bash
docker-compose -f docker-compose-simple.yml up -d --build
```

### 3. Kiểm tra

- Truy cập: `https://localhost` (với SSL) hoặc `http://localhost:443` (không SSL)
- Kiểm tra containers: `docker-compose ps`
- Xem logs: `docker-compose logs -f`

## Production Deployment

### 1. Trên VPS/Server

```bash
# Clone repository
git clone [your-repo-url]
cd fe-thaibinh-chatbot-1008-1

# Tạo SSL certificate với domain thật
sudo certbot certonly --standalone -d yourdomain.com

# Copy certificates
sudo mkdir ssl
sudo cp /etc/letsencrypt/live/yourdomain.com/fullchain.pem ssl/cert.pem
sudo cp /etc/letsencrypt/live/yourdomain.com/privkey.pem ssl/key.pem
sudo chown $USER:$USER ssl/*
chmod 600 ssl/*

# Start services
docker-compose up -d --build
```

### 2. Cấu hình Firewall

```bash
# Ubuntu/Debian
sudo ufw allow 80
sudo ufw allow 443

# CentOS/RHEL
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

### 3. Auto-renewal SSL (cho Let's Encrypt)

```bash
# Thêm vào crontab
echo "0 12 * * * /usr/bin/certbot renew --quiet && docker-compose restart nginx" | sudo tee -a /etc/crontab
```

## Monitoring và Maintenance

### Health Check

```bash
# Kiểm tra container status
docker-compose ps

# Kiểm tra logs
docker-compose logs -f nextjs-app
docker-compose logs -f nginx

# Test nginx config
docker-compose exec nginx nginx -t
```

### Performance Monitoring

```bash
# Container resource usage
docker stats

# Disk usage
docker system df

# Cleanup unused images/containers
docker system prune -f
```

### Backup và Recovery

```bash
# Backup source code và config
tar -czf backup-$(date +%Y%m%d).tar.gz . --exclude=node_modules --exclude=.next

# Recovery
tar -xzf backup-YYYYMMDD.tar.gz
docker-compose up -d --build
```

## Troubleshooting

### Container không start

```bash
# Kiểm tra logs
docker-compose logs nextjs-app

# Kiểm tra port conflicts
netstat -tlnp | grep :443
netstat -tlnp | grep :80

# Restart services
docker-compose restart
```

### SSL Issues

```bash
# Kiểm tra certificate
openssl x509 -in ssl/cert.pem -text -noout

# Test SSL connection
openssl s_client -connect localhost:443

# Recreate self-signed cert
rm -rf ssl/*
./docker-manage.sh ssl
```

### Nginx Issues

```bash
# Test nginx config
docker-compose exec nginx nginx -t

# Reload nginx
docker-compose exec nginx nginx -s reload

# Recreate nginx container
docker-compose up -d --force-recreate nginx
```

### Performance Issues

```bash
# Kiểm tra resource usage
docker stats

# Scale if needed (tăng replicas)
docker-compose up -d --scale nextjs-app=2

# Clean up
docker system prune -f
docker volume prune -f
```

## Security Best Practices

1. **SSL/TLS**: Luôn sử dụng SSL trong production
2. **Firewall**: Chỉ mở port 80, 443
3. **Updates**: Thường xuyên update Docker images
4. **Monitoring**: Setup log monitoring và alerts
5. **Backup**: Backup thường xuyên source code và data

## Scaling

### Horizontal Scaling

```bash
# Chạy multiple instances
docker-compose up -d --scale nextjs-app=3

# Load balancer sẽ tự động distribute traffic
```

### Vertical Scaling

```yaml
# Trong docker-compose.yml, thêm:
services:
  nextjs-app:
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
```
