"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Head<PERSON> } from "@/components/shared/header";
import { Footer } from "@/components/shared/footer";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { HelpCircle, Lock, MessageCircle, Phone } from "lucide-react";

interface FAQ {
  question: string;
  answer: string;
  icon: React.ReactNode;
  color: string;
}

const FAQItem = ({
  question,
  answer,
  isOpen,
  toggleOpen,
  icon,
  color,
}: {
  question: string;
  answer: string;
  isOpen: boolean;
  toggleOpen: () => void;
  icon: React.ReactNode;
  color: string;
}) => (
  <Card
    className={`border-2 border-blue-100 shadow-lg rounded-2xl transition-all duration-200 ${isOpen ? "scale-[1.02] shadow-xl" : ""}`}
  >
    <CardHeader
      onClick={toggleOpen}
      className={`cursor-pointer flex items-center gap-4 px-6 py-4 group hover:bg-gradient-to-r from-blue-50 to-cyan-50 transition-colors duration-200 rounded-t-2xl`}
    >
      <div className={`w-10 h-10 flex items-center justify-center rounded-xl ${color} bg-opacity-80 text-white text-xl shadow group-hover:scale-110 transition-transform`}>{icon}</div>
      <CardTitle className="text-lg font-semibold text-blue-800 group-hover:text-blue-900">
        {question}
      </CardTitle>
    </CardHeader>
    {isOpen && (
      <CardContent className="px-6 pb-6 pt-2">
        <div className="bg-blue-50/80 rounded-xl p-4 text-gray-700 text-base leading-relaxed border border-blue-100">
          {answer}
        </div>
      </CardContent>
    )}
  </Card>
);

export default function FAQPage() {
  const [openFAQ, setOpenFAQ] = useState<number | null>(null);
  const router = useRouter();

  const faqs: FAQ[] = [
    {
      question: "1. Chính sách bảo mật là gì?",
      answer:
        "Chính sách bảo mật của chúng tôi giải thích cách thu thập, sử dụng và bảo vệ thông tin cá nhân của người dùng.",
      icon: <Lock className="w-6 h-6" />,
      color: "bg-gradient-to-br from-blue-500 to-cyan-500",
    },
    {
      question: "2. Làm thế nào để liên hệ với bộ phận hỗ trợ?",
      answer:
        "Bạn có thể gửi email đến địa chỉ hỗ trợ <EMAIL> hoặc gọi số hotline 0356090056.",
      icon: <Phone className="w-6 h-6" />,
      color: "bg-gradient-to-br from-green-500 to-blue-400",
    },
    {
      question: "3. Chatbot có hỗ trợ 24/7 không?",
      answer:
        "Đúng vậy, chatbot của chúng tôi hỗ trợ người dùng mọi lúc, mọi nơi.",
      icon: <MessageCircle className="w-6 h-6" />,
      color: "bg-gradient-to-br from-purple-500 to-blue-400",
    },
    {
      question: "4. Chatbot này có thể trả lời những câu hỏi nào?",
      answer:
        "Chatbot có thể trả lời các câu hỏi liên quan đến thông tin trường học, hoạt động tình nguyện, sự kiện và nhiều chủ đề khác.",
      icon: <HelpCircle className="w-6 h-6" />,
      color: "bg-gradient-to-br from-pink-500 to-blue-400",
    },
  ];

  return (
    <div className="min-h-screen bg-white">
      <Header />

      {/* Hero Section */}
      <section className="mttq-section-header">
        <div className="max-w-7xl mx-auto px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-white mb-2">
              Câu hỏi thường gặp
            </h1>
            <p className="text-white/90 text-lg">
              Giải đáp nhanh các thắc mắc về hệ thống báo cáo MTTQ
            </p>
          </div>
        </div>
      </section>

      <div className="container mx-auto max-w-4xl px-6 py-12">
        <div className="mttq-form-section">

          <div className="p-6 space-y-6">
            {faqs.map((faq, index) => (
              <FAQItem
                key={index}
                question={faq.question}
                answer={faq.answer}
                isOpen={openFAQ === index}
                toggleOpen={() => setOpenFAQ(openFAQ === index ? null : index)}
                icon={faq.icon}
                color={faq.color}
              />
            ))}

            <div className="mt-10 text-center">
              <Button
                className="mttq-button-primary w-full"
                onClick={() => router.push("/")}
              >
                ⬅️ Trở về trang chính
              </Button>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
}
