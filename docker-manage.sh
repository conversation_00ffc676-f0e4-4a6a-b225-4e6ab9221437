#!/bin/bash

# Script quản lý Docker containers cho dự án Thai Binh Chatbot

case "$1" in
    "start")
        echo "🚀 Starting Thai Binh Chatbot..."
        docker-compose up -d --build
        echo "✅ Application started successfully!"
        echo "🌐 Access your app at: https://localhost"
        ;;
    "stop")
        echo "🛑 Stopping Thai Binh Chatbot..."
        docker-compose down
        echo "✅ Application stopped successfully!"
        ;;
    "restart")
        echo "🔄 Restarting Thai Binh Chatbot..."
        docker-compose down
        docker-compose up -d --build
        echo "✅ Application restarted successfully!"
        ;;
    "logs")
        echo "📋 Showing logs..."
        docker-compose logs -f
        ;;
    "status")
        echo "📊 Container status:"
        docker-compose ps
        ;;
    "clean")
        echo "🧹 Cleaning up..."
        docker-compose down
        docker system prune -f
        echo "✅ Cleanup completed!"
        ;;
    "ssl")
        echo "🔒 Creating self-signed SSL certificate..."
        mkdir -p ssl
        openssl req -x509 -newkey rsa:4096 -keyout ssl/key.pem -out ssl/cert.pem -days 365 -nodes \
            -subj "/C=VN/ST=Thai Binh/L=Thai Binh/O=Thai Binh Chatbot/CN=localhost"
        chmod 600 ssl/*
        echo "✅ SSL certificate created successfully!"
        ;;
    *)
        echo "Thai Binh Chatbot Docker Management Script"
        echo ""
        echo "Usage: $0 {start|stop|restart|logs|status|clean|ssl}"
        echo ""
        echo "Commands:"
        echo "  start   - Build and start the application"
        echo "  stop    - Stop the application"
        echo "  restart - Restart the application"
        echo "  logs    - Show application logs"
        echo "  status  - Show container status"
        echo "  clean   - Stop containers and clean up Docker system"
        echo "  ssl     - Create self-signed SSL certificate"
        echo ""
        exit 1
        ;;
esac
