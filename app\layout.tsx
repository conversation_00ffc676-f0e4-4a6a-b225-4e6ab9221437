import type React from "react";
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import { ThemeProvider } from "@/components/theme-provider";
import { SidebarProvider } from "@/components/ui/sidebar";
import "./globals.css";

const inter = Inter({ subsets: ["latin", "vietnamese"] });

export const metadata: Metadata = {
  metadataBase: new URL('http://localhost:3002'),
  title: "Chatbot Mặt trận tổ quốc",
  description: "Tr<PERSON> lý AI tra cứu thông tin về MTTQ Việt Nam",
  generator: "v0.dev",
  keywords: ["MTTQ", "Mặt trận tổ quốc", "Đ<PERSON><PERSON><PERSON> than<PERSON> niên", "Chatbot", "Việt Nam"],
  authors: [{ name: "MTTQ Việt Nam" }],
  creator: "MTTQ Việt Nam",
  publisher: "MTTQ Việt Nam",
  icons: {
    icon: [
      {
        url: "/Logo-MTTQ.png",
        sizes: "32x32",
        type: "image/png",
      },
      {
        url: "/Logo-MTTQ.png",
        sizes: "16x16",
        type: "image/png",
      },
    ],
    shortcut: "/Logo-MTTQ.png",
    apple: {
      url: "/Logo-MTTQ.png",
      sizes: "180x180",
      type: "image/png",
    },
  },
  openGraph: {
    title: "Chatbot Mặt trận tổ quốc",
    description: "Trợ lý AI tra cứu thông tin về MTTQ Việt Nam",
    url: "https://mttq-chatbot.com",
    siteName: "Chatbot MTTQ",
    images: [
      {
        url: "/Logo-MTTQ.png",
        width: 800,
        height: 600,
        alt: "Logo Mặt trận tổ quốc Việt Nam",
      },
    ],
    locale: "vi_VN",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Chatbot Mặt trận tổ quốc",
    description: "Trợ lý AI tra cứu thông tin về MTTQ Việt Nam",
    images: ["/Logo-MTTQ.png"],
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="vi" suppressHydrationWarning>
      <head>
        <link rel="manifest" href="/manifest.json" />
        <meta name="theme-color" content="#b91c1c" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="Chatbot MTTQ" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
      </head>
      <body className={inter.className}>
        <ThemeProvider attribute="class" defaultTheme="light" enableSystem>
          <SidebarProvider>{children}</SidebarProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}


