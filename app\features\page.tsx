"use client";

import Image from "next/image";
import Link from "next/link";
import { Header } from "@/components/shared/header";
import { Footer } from "@/components/shared/footer";

export default function FeaturesPage() {
  return (
    <div className="min-h-screen bg-white">
      <Header />

      {/* Hero Section */}
      <section className="mttq-section-header">
        <div className="max-w-7xl mx-auto px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-white mb-2">
              Tính năng hệ thống
            </h1>
            <p className="text-white/90 text-lg">
              Các tính năng hỗ trợ báo cáo và khai báo thông tin MTTQ
            </p>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-6 lg:px-8 py-12">

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {/* Báo cáo tự động */}
          <div className="mttq-form-section">
            <div className="mttq-section-header rounded-t-lg">
              <h3 className="text-lg font-semibold">Báo cáo tự động</h3>
            </div>
            <div className="p-6">
              <p className="text-gray-700 mb-4">
                Hệ thống tự động tạo báo cáo định kỳ và gửi thông báo nhắc nhở
                cho các tổ chức MTTQ.
              </p>
              <div className="space-y-2">
                <div className="flex items-center text-sm text-gray-600">
                  <span className="w-2 h-2 mttq-bg-primary rounded-full mr-2"></span>
                  Báo cáo hàng tháng
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <span className="w-2 h-2 mttq-bg-primary rounded-full mr-2"></span>
                  Thông báo nhắc nhở
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <span className="w-2 h-2 mttq-bg-primary rounded-full mr-2"></span>
                  Xuất file báo cáo
                </div>
              </div>
            </div>
          </div>

          {/* Viết và đánh giá bài văn nghị luận */}
          <div className="bg-white rounded-2xl shadow-lg p-8 hover:shadow-xl transition-shadow">
            <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center mb-6">
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Viết và đánh giá bài văn nghị luận bằng AI
            </h3>
            <p className="text-gray-600 mb-6">
              Hỗ trợ viết và đánh giá bài văn nghị luận, báo cáo hoạt động, và các văn bản chính thức cho các hoạt động Đoàn - Hội.
            </p>
            <div className="space-y-2">
              <div className="flex items-center text-sm text-gray-500">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                Viết báo cáo hoạt động
              </div>
              <div className="flex items-center text-sm text-gray-500">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                Đánh giá và sửa lỗi ngữ pháp
              </div>
              <div className="flex items-center text-sm text-gray-500">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                Gợi ý cải thiện văn phong
              </div>
            </div>
          </div>

          {/* Tra cứu luật pháp */}
          <div className="bg-white rounded-2xl shadow-lg p-8 hover:shadow-xl transition-shadow">
            <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center mb-6">
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
              </svg>
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Tra cứu luật pháp bằng AI
            </h3>
            <p className="text-gray-600 mb-6">
              Tra cứu nhanh chóng các văn bản pháp luật, quy định liên quan đến hoạt động Đoàn - Hội và quyền lợi của đoàn viên, hội viên.
            </p>
            <div className="space-y-2">
              <div className="flex items-center text-sm text-gray-500">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                Tìm kiếm văn bản pháp luật
              </div>
              <div className="flex items-center text-sm text-gray-500">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                Giải thích điều khoản phức tạp
              </div>
              <div className="flex items-center text-sm text-gray-500">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                Tư vấn quyền lợi đoàn viên
              </div>
            </div>
          </div>
        </div>

        {/* Coming Soon Section */}
        <div className="bg-white rounded-2xl shadow-lg p-8 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Sắp ra mắt
          </h2>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Chúng tôi đang không ngừng phát triển để mang đến cho bạn những tính năng tốt nhất. 
            Hãy theo dõi để cập nhật những tính năng mới nhất!
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/chat"
              className="inline-block bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Trải nghiệm ngay
            </Link>
            <Link
              href="/"
              className="inline-block border border-gray-300 text-gray-700 px-8 py-3 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Về trang chủ
            </Link>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
