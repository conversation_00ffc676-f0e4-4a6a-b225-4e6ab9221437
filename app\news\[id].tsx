import { notFound } from "next/navigation";
import { Header } from "@/components/shared/header";
import Image from "next/image";

// Import danh sách bài viết từ file chung
const newsItems = [
  {
    id: 1,
    title:
      '<PERSON><PERSON><PERSON> thảo khoa học quốc tế "Công nghệ bền vững và nổi bật cho sản xuất thông minh"',
    date: "11:46 23/04/2025",
    summary:
      'Ng<PERSON>y 22/4, tại Trường Đại học <PERSON>hi<PERSON>, <PERSON><PERSON><PERSON> thảo Quốc tế lần thứ hai "Công nghệ bền vững và nổi bật cho sản xuất thông minh" (SETSM 2025) đã được tổ chức.',
    category: "Tin tức",
    image: "/hoithao.jpg",
    content: "Chi tiết hội thảo về công nghệ sản xuất thông minh...",
  },
  {
    id: 2,
    title: "Thông tin tuyển sinh đại học năm 2025",
    date: "05:13 29/04/2025",
    summary:
      "Trường Đại học Công nghiệp Hà Nội công bố Thông tin tuyển sinh đại học năm 2025.",
    category: "Tin tức",
    image: "/t107205.jpg",
    content: "Chi tiết thông tin tuyển sinh...",
  },
  {
    id: 3,
    title: "Thông tin tuyển sinh đại học năm 2025",
    date: "05:13 29/04/2025",
    summary:
      "Trường Đại học Công nghiệp Hà Nội công bố Thông tin tuyển sinh đại học năm 2025.",
    category: "Tin tức",
    image: "/t107205.jpg",
    content: "Chi tiết thông tin tuyển sinh...",
  },
  {
    id: 4,
    title: "Thông tin tuyển sinh đại học năm 2025",
    date: "05:13 29/04/2025",
    summary:
      "Trường Đại học Công nghiệp Hà Nội công bố Thông tin tuyển sinh đại học năm 2025.",
    category: "Tin tức",
    image: "/t107205.jpg",
    content: "Chi tiết thông tin tuyển sinh...",
  },
  {
    id: 5,
    title: "Thông tin tuyển sinh đại học năm 2025",
    date: "05:13 29/04/2025",
    summary:
      "Trường Đại học Công nghiệp Hà Nội công bố Thông tin tuyển sinh đại học năm 2025.",
    category: "Tin tức",
    image: "/t107205.jpg",
    content: "Chi tiết thông tin tuyển sinh...",
  },
  {
    id: 6,
    title: "Thông tin tuyển sinh đại học năm 2025",
    date: "05:13 29/04/2025",
    summary:
      "Trường Đại học Công nghiệp Hà Nội công bố Thông tin tuyển sinh đại học năm 2025.",
    category: "Tin tức",
    image: "/t107205.jpg",
    content: "Chi tiết thông tin tuyển sinh...",
  },
  {
    id: 7,
    title: "Thông tin tuyển sinh đại học năm 2025",
    date: "05:13 29/04/2025",
    summary:
      "Trường Đại học Công nghiệp Hà Nội công bố Thông tin tuyển sinh đại học năm 2025.",
    category: "Tin tức",
    image: "/t107205.jpg",
    content: "Chi tiết thông tin tuyển sinh...",
  },
  {
    id: 8,
    title: "Thông tin tuyển sinh đại học năm 2025",
    date: "05:13 29/04/2025",
    summary:
      "Trường Đại học Công nghiệp Hà Nội công bố Thông tin tuyển sinh đại học năm 2025.",
    category: "Tin tức",
    image: "/t107205.jpg",
    content: "Chi tiết thông tin tuyển sinh...",
  },
  {
    id: 9,
    title: "Thông tin tuyển sinh đại học năm 2025",
    date: "05:13 29/04/2025",
    summary:
      "Trường Đại học Công nghiệp Hà Nội công bố Thông tin tuyển sinh đại học năm 2025.",
    category: "Tin tức",
    image: "/t107205.jpg",
    content: "Chi tiết thông tin tuyển sinh...",
  },
  {
    id: 10,
    title: "Thông tin tuyển sinh đại học năm 2025",
    date: "05:13 29/04/2025",
    summary:
      "Trường Đại học Công nghiệp Hà Nội công bố Thông tin tuyển sinh đại học năm 2025.",
    category: "Tin tức",
    image: "/t107205.jpg",
    content: "Chi tiết thông tin tuyển sinh...",
  },
  {
    id: 11,
    title: "Thông tin tuyển sinh đại học năm 2025",
    date: "05:13 29/04/2025",
    summary:
      "Trường Đại học Công nghiệp Hà Nội công bố Thông tin tuyển sinh đại học năm 2025.",
    category: "Tin tức",
    image: "/t107205.jpg",
    content: "Chi tiết thông tin tuyển sinh...",
  },
  {
    id: 12,
    title: "Thông tin tuyển sinh đại học năm 2025",
    date: "05:13 29/04/2025",
    summary:
      "Trường Đại học Công nghiệp Hà Nội công bố Thông tin tuyển sinh đại học năm 2025.",
    category: "Tin tức",
    image: "/t107205.jpg",
    content: "Chi tiết thông tin tuyển sinh...",
  },
  {
    id: 13,
    title: "Thông tin tuyển sinh đại học năm 2025",
    date: "05:13 29/04/2025",
    summary:
      "Trường Đại học Công nghiệp Hà Nội công bố Thông tin tuyển sinh đại học năm 2025.",
    category: "Tin tức",
    image: "/t107205.jpg",
    content: "Chi tiết thông tin tuyển sinh...",
  },
  {
    id: 14,
    title: "Thông tin tuyển sinh đại học năm 2025",
    date: "05:13 29/04/2025",
    summary:
      "Trường Đại học Công nghiệp Hà Nội công bố Thông tin tuyển sinh đại học năm 2025.",
    category: "Tin tức",
    image: "/t107205.jpg",
    content: "Chi tiết thông tin tuyển sinh...",
  },
  {
    id: 15,
    title: "Thông tin tuyển sinh đại học năm 2025",
    date: "05:13 29/04/2025",
    summary:
      "Trường Đại học Công nghiệp Hà Nội công bố Thông tin tuyển sinh đại học năm 2025.",
    category: "Tin tức",
    image: "/t107205.jpg",
    content: "Chi tiết thông tin tuyển sinh...",
  },
  {
    id: 16,
    title: "Thông tin tuyển sinh đại học năm 2025",
    date: "05:13 29/04/2025",
    summary:
      "Trường Đại học Công nghiệp Hà Nội công bố Thông tin tuyển sinh đại học năm 2025.",
    category: "Tin tức",
    image: "/t107205.jpg",
    content: "Chi tiết thông tin tuyển sinh...",
  },
  {
    id: 17,
    title: "Thông tin tuyển sinh đại học năm 2025",
    date: "05:13 29/04/2025",
    summary:
      "Trường Đại học Công nghiệp Hà Nội công bố Thông tin tuyển sinh đại học năm 2025.",
    category: "Tin tức",
    image: "/t107205.jpg",
    content: "Chi tiết thông tin tuyển sinh...",
  },
  {
    id: 18,
    title: "Thông tin tuyển sinh đại học năm 2025",
    date: "05:13 29/04/2025",
    summary:
      "Trường Đại học Công nghiệp Hà Nội công bố Thông tin tuyển sinh đại học năm 2025.",
    category: "Tin tức",
    image: "/t107205.jpg",
    content: "Chi tiết thông tin tuyển sinh...",
  },
  {
    id: 19,
    title: "Thông tin tuyển sinh đại học năm 2025",
    date: "05:13 29/04/2025",
    summary:
      "Trường Đại học Công nghiệp Hà Nội công bố Thông tin tuyển sinh đại học năm 2025.",
    category: "Tin tức",
    image: "/t107205.jpg",
    content: "Chi tiết thông tin tuyển sinh...",
  },
];

export default function NewsDetailPage({ params }: { params: { id: string } }) {
  const id = parseInt(params.id);
  const news = newsItems.find((item) => item.id === id);

  if (!news) {
    return notFound();
  }

  return (
    <>
      <Header />
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-blue-600">{news.title}</h1>
          <p className="text-sm text-gray-500 mt-2">
            {news.date} | {news.category}
          </p>
        </div>
        <div className="relative w-full h-[400px] mb-6">
          <Image
            src={news.image}
            alt={news.title}
            fill
            className="rounded-lg object-cover"
            priority
          />
        </div>
        <div className="text-lg leading-relaxed text-gray-800">
          <p className="mb-4">{news.summary}</p>
          <p>{news.content}</p>
        </div>
      </div>
    </>
  );
}
