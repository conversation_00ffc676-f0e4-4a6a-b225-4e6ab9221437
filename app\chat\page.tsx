"use client";

import Image from "next/image";
import Link from "next/link";




import { Chatbot } from "@/components/Chatbot";
import { AppSidebar } from "@/components/Sidebar";
import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";

export default function ChatPage() {
  return (
    <SidebarProvider>
      <div className="flex min-h-screen bg-white">
        <AppSidebar />
        <div className="flex-1 flex flex-col">
          {/* Header with menu and navigation */}
          <header className="sticky top-0 mttq-header border-b border-gray-200 z-10">
            <div className="max-w-5xl mx-auto px-6 py-3 flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <SidebarTrigger className="mr-2 text-white hover:text-white/80" />
                <Image src="/Logo-MTTQ.png" alt="Logo MTTQ" width={32} height={32} className="rounded-full" />
                <h2 className="text-lg font-semibold text-white ml-2">Chatbot Mặt trận tổ quốc</h2>
              </div>
              <nav className="hidden md:flex space-x-6">
                <Link href="/" className="text-white/80 hover:text-white">Trang chủ</Link>
                <Link href="/chat" className="text-white font-medium">Chatbot</Link>
                <Link href="/about" className="text-white/80 hover:text-white">Giới thiệu</Link>
              </nav>
            </div>
          </header>
          {/* Content: Chatbot fills remaining space */}
          <div className="flex-1 flex justify-center bg-white">
            <div className="w-full max-w-3xl bg-white min-h-[calc(100vh-64px)]">
              <Chatbot chatType="docs-rag" />
            </div>
          </div>
        </div>
      </div>
    </SidebarProvider>
  );
}