@import url("https://fonts.googleapis.com/css2?family=Nunito:wght@400;500;600;700&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 0 0% 100%;
  --foreground: 0 0% 9%;
  --muted: 0 0% 96%;
  --muted-foreground: 0 0% 45%;
  --popover: 0 0% 100%;
  --popover-foreground: 0 0% 9%;
  --card: 0 0% 100%;
  --card-foreground: 0 0% 9%;
  --border: 0 0% 90%;
  --input: 0 0% 90%;
  --primary: 345 85% 35%;
  --primary-foreground: 0 0% 100%;
  --secondary: 0 0% 96%;
  --secondary-foreground: 0 0% 9%;
  --accent: 0 0% 96%;
  --accent-foreground: 0 0% 9%;
  --destructive: 0 84% 60%;
  --destructive-foreground: 0 0% 98%;
  --ring: 345 85% 35%;
  --radius: 0.5rem;

  /* Custom deep red color variables */
  --deep-red: 345 85% 35%;
  --deep-red-dark: 345 85% 30%;
  --deep-red-light: 345 85% 40%;
  --wine-red: 340 80% 32%;
  --plum-red: 350 75% 38%;
}

.dark {
  --background: 224 71% 4%;
  --foreground: 213 31% 91%;
  --muted: 223 47% 11%;
  --muted-foreground: 215.4 16.3% 56.9%;
  --popover: 224 71% 4%;
  --popover-foreground: 215 20.2% 65.1%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 47.4% 11.2%;
  --border: 216 34% 17%;
  --input: 216 34% 17%;
  --primary: 210 40% 98%;
  --primary-foreground: 222.2 47.4% 1.2%;
  --secondary: 222.2 47.4% 11.2%;
  --secondary-foreground: 210 40% 98%;
  --accent: 216 34% 17%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 63% 31%;
  --destructive-foreground: 210 40% 98%;
  --ring: 216 34% 17%;
  --radius: 0.5rem;
}

body {
  @apply bg-background text-foreground;
  font-family: "Nunito", sans-serif;
}

.markdown-content {
  @apply max-w-none;
}

.markdown-content pre {
  @apply bg-gray-800 text-white p-4 rounded-xl overflow-x-auto;
}

.markdown-content code {
  @apply bg-gray-100 text-gray-800 px-1 py-0.5 rounded;
}
.typing-animation {
  display: flex;
  align-items: center;
  column-gap: 6px;
}

.typing-animation span {
  height: 8px;
  width: 8px;
  background-color: #8e8e8e;
  border-radius: 50%;
  display: block;
  opacity: 0.4;
}

.typing-animation span:nth-child(1) {
  animation: pulse 1s infinite ease-in-out;
}

.typing-animation span:nth-child(2) {
  animation: pulse 1s infinite ease-in-out 0.2s;
}

.typing-animation span:nth-child(3) {
  animation: pulse 1s infinite ease-in-out 0.4s;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.4;
  }
  50% {
    transform: scale(1.3);
    opacity: 1;
  }
}
/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

@layer components {
  .markdown-content {
    @apply prose prose-sm;
  }

  /* Custom shadow utilities */
  .shadow-3xl {
    box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.05);
  }

  /* Glassmorphism effect */
  .glass-effect {
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.25);
    border: 1px solid rgba(255, 255, 255, 0.18);
  }

  /* Deep Red Design System Classes */
  .mttq-header {
    @apply text-white px-6 py-4;
    background-color: hsl(345, 85%, 35%) !important;
  }

  .mttq-nav {
    background-color: hsl(345, 85%, 35%) !important;
  }

  .mttq-button-primary {
    @apply text-white font-medium px-6 py-3 rounded-lg transition-colors duration-200;
    background-color: hsl(345, 85%, 35%) !important;
  }

  .mttq-button-primary:hover {
    background-color: hsl(345, 85%, 30%) !important;
  }

  .mttq-section-header {
    @apply text-white px-6 py-4 font-semibold text-lg;
    background-color: hsl(345, 85%, 35%) !important;
  }

  .mttq-input {
    @apply border border-gray-300 rounded-md px-4 py-2 focus:ring-2 focus:border-transparent transition-all duration-200;
  }

  .mttq-input:focus {
    --tw-ring-color: hsl(345, 85%, 35%, 0.3);
    border-color: hsl(345, 85%, 35%) !important;
  }

  /* Enhanced input styling for chatbot */
  .mttq-input-enhanced {
    @apply transition-all duration-300 ease-in-out;
    font-family: "Nunito", sans-serif;
    font-weight: 400;
    letter-spacing: 0.01em;
  }

  .mttq-input-enhanced::placeholder {
    @apply text-gray-500 transition-colors duration-300;
    font-weight: 400;
  }

  .mttq-input-enhanced:focus::placeholder {
    @apply text-gray-400;
  }

  .mttq-input-enhanced:disabled {
    @apply text-gray-400 cursor-not-allowed;
    background-color: transparent;
  }

  /* Enhanced input container hover and focus states */
  .mttq-input-container {
    @apply transition-all duration-300 ease-in-out;
  }

  .mttq-input-container:hover {
    @apply shadow-xl;
    transform: translateY(-1px);
  }

  .mttq-input-container:focus-within {
    @apply shadow-xl;
    border-color: hsl(345, 85%, 35%) !important;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04), 0 0 0 3px hsl(345, 85%, 35%, 0.1);
    transform: translateY(-1px);
  }

  /* Enhanced send button styling */
  .mttq-send-button {
    @apply flex items-center justify-center;
    min-width: 3rem;
    min-height: 3rem;
  }

  .mttq-send-button:not(:disabled):hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px -5px rgba(0, 0, 0, 0.15), 0 0 0 2px hsl(345, 85%, 35%, 0.1);
  }

  .mttq-send-button:not(:disabled):active {
    transform: translateY(0) scale(0.95);
    transition-duration: 150ms;
  }

  /* Mobile responsive adjustments */
  @media (max-width: 640px) {
    .mttq-input-enhanced {
      font-size: 16px; /* Prevents zoom on iOS */
      line-height: 1.5;
    }

    .mttq-send-button {
      min-width: 2.75rem;
      min-height: 2.75rem;
    }
  }

  /* Enhanced focus states for accessibility */
  .mttq-input-enhanced:focus {
    outline: none;
  }

  /* Smooth transitions for all interactive elements */
  .mttq-input-enhanced,
  .mttq-send-button,
  .mttq-input-container {
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }

  .mttq-card {
    @apply bg-white border border-gray-200 rounded-lg shadow-sm;
  }

  .mttq-text-primary {
    color: hsl(345, 85%, 35%) !important;
  }

  .mttq-bg-primary {
    background-color: hsl(345, 85%, 35%) !important;
  }

  .mttq-border-primary {
    border-color: hsl(345, 85%, 35%) !important;
  }

  /* Professional form styling */
  .mttq-form-section {
    @apply bg-white border border-gray-200 rounded-lg p-6 mb-6;
  }

  .mttq-footer {
    @apply bg-white border-t border-gray-200 text-gray-600;
  }

  .mttq-footer-link {
    @apply text-blue-600 hover:text-blue-800 transition-colors duration-200;
  }

  .mttq-footer-contact {
    color: hsl(345, 85%, 35%) !important;
    @apply font-medium;
  }

  /* Gradient hover effects */
  .gradient-hover {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .gradient-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 40px -12px rgba(0, 0, 0, 0.12);
  }
}


