"use client";

import Image from "next/image";
import Link from "next/link";
import { Header } from "@/components/shared/header";
import { Footer } from "@/components/shared/footer";



export default function HomePage() {
  return (
    <div className="min-h-screen bg-white">
      <Header />

      {/* Hero Section */}
      <section className="mttq-section-header">
        <div className="max-w-7xl mx-auto px-6 lg:px-8 py-8">
          <div className="flex items-center space-x-4">
            <Image
              src="/Logo-MTTQ.png"
              alt="Logo MTTQ"
              width={80}
              height={80}
              className="rounded-full"
            />
            <div>
              <h1 className="text-3xl font-bold text-white">
                Báo cáo số liệu tổ chức
              </h1>
              <p className="text-white/90 text-lg">
                Hệ thống báo cáo và khai báo thông tin MTTQ Việt Nam
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Left Column - Main Information */}
          <div className="space-y-8">
            <div className="mttq-form-section">
              <div className="mttq-section-header rounded-t-lg">
                <h2 className="text-xl font-semibold">Khai báo thông tin</h2>
              </div>
              <div className="p-6">
                <p className="text-gray-700 mb-6">
                  Hệ thống hỗ trợ khai báo và báo cáo số liệu hoạt động của các tổ chức MTTQ.
                  Vui lòng sử dụng chatbot để được hướng dẫn chi tiết về quy trình báo cáo.
                </p>
                <ul className="list-disc list-inside text-gray-700 mb-6 space-y-2">
                  <li>Báo cáo số liệu tổ chức định kỳ</li>
                  <li>Khai báo thông tin hoạt động</li>
                  <li>Tra cứu hướng dẫn và quy định</li>
                  <li>Hỗ trợ kỹ thuật 24/7</li>
                </ul>
                <Link
                  href="/chat"
                  className="mttq-button-primary inline-block text-center"
                >
                  Truy cập Chatbot ngay
                </Link>
              </div>
            </div>
          </div>

          {/* Right Column - Features and Support */}
          <div className="space-y-8">
            <div className="mttq-form-section">
              <div className="mttq-section-header rounded-t-lg">
                <h2 className="text-xl font-semibold">Báo cáo số liệu</h2>
              </div>
              <div className="p-6">
                <p className="text-gray-700 mb-6">
                  Hệ thống báo cáo số liệu hiện đại, đảm bảo tính chính xác và kịp thời
                  trong việc thu thập và xử lý thông tin hoạt động MTTQ.
                </p>
                <ul className="list-disc list-inside text-gray-700 mb-6 space-y-2">
                  <li>Giao diện đơn giản, dễ sử dụng</li>
                  <li>Báo cáo tự động và theo lịch</li>
                  <li>Tích hợp với hệ thống quản lý</li>
                </ul>
                <Link
                  href="/features"
                  className="mttq-text-primary hover:underline font-medium"
                >
                  Tìm hiểu thêm →
                </Link>
              </div>
            </div>

            <div className="mttq-card p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4">
                Hỗ trợ kỹ thuật
              </h2>
              <p className="text-gray-700 mb-4">
                Đội ngũ hỗ trợ kỹ thuật sẵn sàng giúp đỡ bạn trong quá trình sử dụng hệ thống.
              </p>
              <div className="space-y-2">
                <p className="text-gray-700">
                  <span className="font-medium">Hotline:</span>
                  <a href="tel:0356090056" className="mttq-footer-contact ml-2">0356 090 056</a>
                </p>
                <p className="text-gray-700">
                  <span className="font-medium">Email:</span>
                  <a href="mailto:<EMAIL>" className="mttq-footer-link ml-2"><EMAIL></a>
                </p>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
