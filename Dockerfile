# Sử dụng Node.js image ch<PERSON>h thức
FROM node:18-alpine AS base

# Cài đặt dependencies cần thiết
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Cài đặt dependencies
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Cài đặt dependencies dựa trên package manager đ<PERSON><PERSON><PERSON> sử dụng
COPY package.json package-lock.json* ./
RUN \
  if [ -f package-lock.json ]; then npm ci; \
  else echo "Lockfile not found." && exit 1; \
  fi

# Cài đặt chỉ production dependencies
FROM base AS production-deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json* ./
RUN \
  if [ -f package-lock.json ]; then npm ci --omit=dev; \
  else echo "Lockfile not found." && exit 1; \
  fi

# Xây dựng ứng dụng
FROM base AS builder
WORKDIR /app

# Copy dependencies từ stage trước
COPY --from=deps /app/node_modules ./node_modules

# Copy tất cả source code và các file cấu hình
COPY . .

# Disable telemetry trong quá trình build
ENV NEXT_TELEMETRY_DISABLED 1

# Build ứng dụng
RUN npm run build

# Production image, copy tất cả files cần thiết
FROM base AS runner
WORKDIR /app

# Thiết lập môi trường production
ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

# Tạo user và group để chạy ứng dụng an toàn
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copy thư mục public
COPY --from=builder /app/public ./public

# Tạo thư mục .next và set quyền
RUN mkdir -p .next
RUN chown nextjs:nodejs .next

# Copy production dependencies
COPY --from=production-deps /app/node_modules ./node_modules
COPY --from=production-deps /app/package.json ./package.json

# Tự động leverage output traces để giảm image size
# Copy standalone output (chỉ những file cần thiết)
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./

# Copy static assets
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# Chuyển sang user non-root
USER nextjs

# Mở port 3000
EXPOSE 3000

# Thiết lập environment variables
ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

# Chạy server production
CMD ["node", "server.js"]