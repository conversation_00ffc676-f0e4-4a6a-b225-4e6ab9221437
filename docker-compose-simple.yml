version: '3.8'

services:
  nextjs-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: thaibinh-chatbot
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - NEXT_TELEMETRY_DISABLED=1
    networks:
      - app-network

  nginx:
    image: nginx:alpine
    container_name: nginx-proxy
    restart: unless-stopped
    ports:
      - "443:443"
      - "80:80"
    volumes:
      - ./nginx-simple.conf:/etc/nginx/nginx.conf:ro
    networks:
      - app-network
    depends_on:
      - nextjs-app

networks:
  app-network:
    driver: bridge
