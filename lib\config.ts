import appConfig from "@/config/config.json";

export type Environment = "development" | "staging" | "production";

type ApiConfig = {
  baseUrl: string;
  headers?: Record<string, string>;
  timeoutMs?: number;
  endpoints: Record<string, string>;
};

type RootConfig = {
  environments: Record<Environment, { api: ApiConfig }>;
};

const config = appConfig as unknown as RootConfig;

export function getEnv(): Environment {
  const fromPublic = process.env.NEXT_PUBLIC_APP_ENV as Environment | undefined;
  const fromNode = process.env.NODE_ENV as Environment | undefined;
  if (fromPublic && ["development", "staging", "production"].includes(fromPublic)) return fromPublic;
  if (fromNode === "production") return "production";
  // Default to development for local/unknown
  return "development";
}

export function getApiConfig(): ApiConfig {
  const env = getEnv();
  return config.environments[env].api;
}

export function getApiBaseUrl(): string {
  return getApiConfig().baseUrl.replace(/\/$/, "");
}

export function getApiHeaders(): Record<string, string> {
  return { ...(getApiConfig().headers || {}) };
}

export function getApiTimeout(): number {
  return getApiConfig().timeoutMs ?? 30000;
}

export function getEndpointPath(key: string): string {
  const path = getApiConfig().endpoints[key];
  if (!path) throw new Error(`Unknown API endpoint key: ${key}`);
  return path.startsWith("/") ? path : `/${path}`;
}

export function getApiUrl(key: string): string {
  return `${getApiBaseUrl()}${getEndpointPath(key)}`;
}

export async function apiFetch(key: string, init: RequestInit = {}): Promise<Response> {
  const url = getApiUrl(key);
  const defaultHeaders = getApiHeaders();
  const mergedHeaders: HeadersInit = { ...defaultHeaders, ...(init.headers || {}) };
  
  // Sử dụng signal từ bên ngoài nếu có, hoặc tạo timeout controller
  let timeoutController: AbortController | undefined;
  let timeoutId: any;
  
  if (!init.signal) {
    // Chỉ tạo timeout controller nếu không có signal từ bên ngoài
    timeoutController = new AbortController();
    const timeout = getApiTimeout();
    timeoutId = setTimeout(() => {
      timeoutController!.abort();
    }, timeout);
  }

  try {
    const response = await fetch(url, {
      ...init,
      headers: mergedHeaders,
      signal: init.signal || timeoutController?.signal,
    });
    return response;
  } catch (error) {
    // Cải thiện error handling
    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        if (!init.signal) {
          // Timeout error
          throw new Error('Request timeout - server đang bận, vui lòng thử lại');
        } else {
          // Request được hủy từ bên ngoài
          throw error;
        }
      } else if (error.message.includes('Failed to fetch')) {
        throw new Error('Không thể kết nối đến server. Vui lòng kiểm tra kết nối mạng.');
      }
    }
    throw error;
  } finally {
    if (timeoutId) clearTimeout(timeoutId);
  }
}

