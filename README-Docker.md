# Docker và Next.js Deployment

## C<PERSON>u trúc Files

- `Dockerfile`: <PERSON><PERSON><PERSON> <PERSON>ình <PERSON> image cho ứng dụng Next.js
- `docker-compose.yml`: Orchestration cho các containers
- `nginx.conf`: <PERSON><PERSON><PERSON> hình <PERSON>inx proxy với SSL
- `.dockerignore`: <PERSON><PERSON><PERSON> trừ files không cần thiết khỏi Docker build

## SSL Certificates

Trước khi chạy, bạn cần t<PERSON>o thư mục `ssl` và thêm SSL certificates:

```bash
mkdir ssl
# Thêm cert.pem và key.pem vào thư mục ssl/
```

### Tạo Self-signed Certificate (cho testing):

```bash
openssl req -x509 -newkey rsa:4096 -keyout ssl/key.pem -out ssl/cert.pem -days 365 -nodes
```

### Sử dụng Let's Encrypt (cho production):

```bash
# Cài đặt certbot
sudo apt-get install certbot

# Tạo certificate
sudo certbot certonly --standalone -d yourdomain.com

# Copy certificates
cp /etc/letsencrypt/live/yourdomain.com/fullchain.pem ssl/cert.pem
cp /etc/letsencrypt/live/yourdomain.com/privkey.pem ssl/key.pem
```

## Cách sử dụng

### 1. Build và chạy containers:

```bash
docker-compose up -d --build
```

### 2. Kiểm tra status:

```bash
docker-compose ps
```

### 3. Xem logs:

```bash
# Tất cả services
docker-compose logs -f

# Chỉ Next.js app
docker-compose logs -f nextjs-app

# Chỉ Nginx
docker-compose logs -f nginx
```

### 4. Dừng containers:

```bash
docker-compose down
```

### 5. Rebuild khi có thay đổi code:

```bash
docker-compose down
docker-compose up -d --build
```

## Port Mapping

- Port 443 (HTTPS): Nginx proxy → Next.js app (port 3000)
- Port 80 (HTTP): Redirect to HTTPS

## Features

- ✅ Multi-stage Docker build để tối ưu image size
- ✅ Nginx reverse proxy
- ✅ HTTP to HTTPS redirect
- ✅ SSL/TLS termination
- ✅ Static file caching
- ✅ Gzip compression
- ✅ Security headers
- ✅ Health checks

## Troubleshooting

### Container không start:

```bash
docker-compose logs nextjs-app
```

### SSL errors:

- Kiểm tra certificates trong thư mục `ssl/`
- Đảm bảo file permissions đúng: `chmod 600 ssl/*`

### Nginx configuration errors:

```bash
# Test nginx config
docker-compose exec nginx nginx -t
```

### Port conflicts:

- Đảm bảo port 80 và 443 không được sử dụng bởi services khác
- Kiểm tra: `netstat -tlnp | grep :443`
