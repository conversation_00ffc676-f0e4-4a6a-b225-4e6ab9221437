"use client"

import { useState, useRef, useEffect, useCallback } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
// Removed inner ScrollArea to allow page-level scrolling
import ReactMarkdown, { Components } from "react-markdown"
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter"
import { atomDark } from "react-syntax-highlighter/dist/esm/styles/prism"
import { Send, Loader2, User, Bot, X } from "lucide-react"
import { format } from "date-fns"
import { vi } from "date-fns/locale"
import { motion, AnimatePresence } from "framer-motion"

interface Message {
  role: "user" | "assistant"
  content: string
  timestamp: Date
}

interface ChatbotProps {
  initialContext?: string;
  chatType: 'docs-rag' | 'ask-in-content';
}

// Define types for code component props
interface CodeProps {
  node?: any; // Keeping 'any' for node as it's complex from remark
  inline?: boolean;
  className?: string;
  children?: React.ReactNode;
}

const MarkdownComponents: Components = {
  code({ node, inline, className, children, ...props }: CodeProps) {
    const match = /language-(\w+)/.exec(className || "")
    return !inline && match ? (
      <SyntaxHighlighter
        style={atomDark}
        language={match[1]}
        PreTag="div"
        {...props}
        customStyle={{
          margin: '1em 0',
          borderRadius: '0.5rem',
          padding: '1em',
          fontSize: '0.9rem',
          lineHeight: 1.5,
        }}
      >
        {String(children).replace(/\n$/, "")}
      </SyntaxHighlighter>
    ) : (
      <code
        {...props}
        className="px-1.5 py-0.5 rounded font-mono text-[0.85em]"
      >
        {children}
      </code>
    )
  },
  a: ({ href, children, ...props }: any) => (
    <a
      href={href}
      target="_blank"
      rel="noopener noreferrer"
      className="no-underline hover:underline"
      {...props}
    >
      {children}
    </a>
  ),
}

export function Chatbot({ initialContext, chatType }: ChatbotProps) {
  const [messages, setMessages] = useState<Message[]>([])
  const [input, setInput] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  // const scrollAreaRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)
  const abortControllerRef = useRef<AbortController | null>(null)

  // Add initial context message based on chatType
  useEffect(() => {
    if (chatType === 'ask-in-content' && initialContext) {
      setMessages([
        {
          role: "assistant",
          content: "Tôi đã sẵn sàng để trả lời các câu hỏi về bài viết này. Bạn có thể hỏi bất kỳ điều gì!",
          timestamp: new Date()
        }
      ]);
    } else if (chatType === 'docs-rag') {
      setMessages([
        {
          role: "assistant",
          content: "Xin chào! Tôi có thể giúp gì cho bạn?",
          timestamp: new Date()
        }
      ]);
    }
  }, [initialContext, chatType]);

  const scrollToBottom = useCallback(() => {
    // Page-level smooth scroll
    try {
      window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' })
    } catch {
      // no-op for SSR/unsupported
    }
  }, [])

  useEffect(() => {
    if (messages.length > 0) {
      // Add a small delay to ensure the DOM has updated and animations are complete
      const scrollTimeout = setTimeout(scrollToBottom, 150)
      return () => clearTimeout(scrollTimeout)
    }
  }, [messages, scrollToBottom])
  // Focus input on component mount
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [])

  // Cleanup: hủy request khi component unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [])

  const handleSendMessage = async () => {
    if (!input.trim() || isLoading) return

    // Hủy request cũ nếu đang chạy
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    // Tạo AbortController mới cho request này
    abortControllerRef.current = new AbortController()

    const userMessageContent = input;
    const newUserMessage: Message = { role: "user", content: userMessageContent, timestamp: new Date() };
    const currentMessages = [...messages, newUserMessage];
    setMessages(currentMessages);
    setInput("");
    setIsLoading(true);
    // scrollToBottom() // Call after assistant placeholder is added

    // Add assistant placeholder immediately
    const assistantPlaceholderMessage: Message = { role: "assistant", content: "...", timestamp: new Date() };
    setMessages([...currentMessages, assistantPlaceholderMessage]);
    scrollToBottom(); // Scroll after user and placeholder message are added

    let endpointKey: string = "";
    let requestBody = {} as Record<string, unknown>;

    if (chatType === 'docs-rag') {
      endpointKey = "docsRagAgent";
      requestBody = {
        text: userMessageContent,
        session: "default_session",
      };
    } else if (chatType === 'ask-in-content') {
      endpointKey = "askInContent";
      requestBody = {
        question: userMessageContent,
        context: initialContext,
      };
    }

    try {
      const { apiFetch } = await import("@/lib/config");
      const response = await apiFetch(endpointKey, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
        signal: abortControllerRef.current?.signal, // Thêm signal để có thể hủy request
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      const assistantResponseContent = chatType === 'docs-rag' ? data.output : data.answer;
      const finalAssistantMessage: Message = { role: "assistant", content: assistantResponseContent, timestamp: new Date() };
      setMessages([...currentMessages, finalAssistantMessage]);
      // scrollToBottom() // Already called after placeholder
    } catch (error) {
      console.error("Error details:", error)
      let errorMessageContent = "Xin lỗi, đã xảy ra lỗi. Vui lòng thử lại."
      
      // Xử lý các loại lỗi khác nhau
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          // Request bị hủy - không hiển thị lỗi cho người dùng
          return;
        } else if (error.message.includes('signal is aborted')) {
          errorMessageContent = "Kết nối bị gián đoạn. Vui lòng kiểm tra mạng và thử lại."
        } else if (error.message.includes('Failed to fetch') || error.message.includes('Network')) {
          errorMessageContent = "Không thể kết nối đến server. Vui lòng kiểm tra kết nối mạng."
        } else if (error.message.includes('timeout')) {
          errorMessageContent = "Request quá lâu. Server có thể đang bận, vui lòng thử lại."
        } else {
          errorMessageContent += ` Chi tiết lỗi: ${error.message}`
        }
      }
      
      // Replace the placeholder with the error message
      const errorAssistantMessage: Message = { role: "assistant", content: errorMessageContent, timestamp: new Date() };
      setMessages([...currentMessages, errorAssistantMessage]);
    } finally {
      setIsLoading(false)
      abortControllerRef.current = null // Reset controller
      // scrollToBottom is called when messages state updates through useEffect
    }
  }

  const handleCancelRequest = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
      setIsLoading(false)
      
      // Xóa placeholder message "..."
      setMessages(prevMessages => {
        const filteredMessages = prevMessages.filter(msg => msg.content !== "...")
        return [...filteredMessages, {
          role: "assistant",
          content: "Đã hủy yêu cầu.",
          timestamp: new Date()
        }]
      })
    }
  }

  return (
    <Card className="w-full h-full min-h-0 flex flex-col bg-transparent">
    <CardContent className="p-4 flex flex-col">
          <div className="space-y-6 py-4 pb-28">
            <AnimatePresence>
              {messages.map((message, index) => (
                <motion.div 
                  key={index} 
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                  className={`flex ${message.role === "user" ? "justify-end" : "justify-start"}`}
                >
                  <div
                    className={`flex items-start space-x-2 max-w-[85%] ${message.role === "user" ? "flex-row-reverse space-x-reverse" : ""}`}
                  >
                    {message.role === "user" ? (
                      <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center shadow-md shrink-0 flex-none aspect-square">
                        <User className="w-5 h-5 text-white" />
                      </div>
                    ) : (
                      <div className="w-8 h-8 rounded-full bg-green-500 flex items-center justify-center shadow-md shrink-0 flex-none aspect-square">
                        <Bot className="w-5 h-5 text-white" />
                      </div>
                    )}
                    <div className="flex flex-col">
                      <div
                        className={`p-3 rounded-2xl shadow-sm ${
                          message.role === "user" 
                            ? "bg-blue-500 text-white rounded-tr-none" 
                            : "bg-white text-gray-800 rounded-tl-none border border-gray-100"
                        }`}
                      >
                        {message.content === "..." ? (
                          <div className="flex items-center space-x-2 px-2 py-1">
                            <div className="typing-animation">
                              <span></span>
                              <span></span>
                              <span></span>
                            </div>
                          </div>
                        ) : (
                          <div
                            className={`${message.role === "user"
                              ? "prose prose-sm md:prose-base max-w-none prose-invert prose-pre:my-3 prose-p:my-2 prose-li:my-1 prose-headings:mt-2 prose-headings:mb-1"
                              : "prose prose-sm md:prose-base max-w-none prose-pre:my-3 prose-p:my-2 prose-li:my-1 prose-headings:mt-2 prose-headings:mb-1"}
                            `}
                          >
                            <ReactMarkdown
                              components={MarkdownComponents}
                              skipHtml
                            >
                              {message.content}
                            </ReactMarkdown>
                          </div>
                        )}
                      </div>
                      <span className={`text-xs text-gray-500 mt-1 ${message.role === "user" ? "text-right" : "text-left"}`}>
                        {format(message.timestamp, 'HH:mm', { locale: vi })}
                      </span>
                    </div>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
          <div ref={messagesEndRef} />
        {/* Fixed input bar at bottom of page, centered to chat width */}
        <div className="fixed bottom-0 left-0 right-0 z-20">
          <div className="mx-auto w-full max-w-3xl px-4">
            {/* Suggestion buttons */}
            {messages.length === 0 && (
              <div className="mb-4 bg-white rounded-lg border border-gray-200 p-4 shadow-sm">
                <h3 className="text-sm font-medium text-gray-700 mb-3">Tôi có thể giúp gì cho bạn?</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                  <Button
                    variant="outline"
                    className="text-left justify-start h-auto p-3 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-50"
                    onClick={() => setInput("Thủ tục gia nhập Đoàn Thanh niên như thế nào?")}
                  >
                    📋 Thủ tục gia nhập Đoàn Thanh niên
                  </Button>
                  <Button
                    variant="outline"
                    className="text-left justify-start h-auto p-3 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-50"
                    onClick={() => setInput("Quyền và nghĩa vụ của Đoàn viên là gì?")}
                  >
                    ⚖️ Quyền và nghĩa vụ Đoàn viên
                  </Button>
                  <Button
                    variant="outline"
                    className="text-left justify-start h-auto p-3 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-50"
                    onClick={() => setInput("Hoạt động của Mặt trận tổ quốc có những gì?")}
                  >
                    🏛️ Hoạt động Mặt trận tổ quốc
                  </Button>
                  <Button
                    variant="outline"
                    className="text-left justify-start h-auto p-3 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-50"
                    onClick={() => setInput("Cách thức tham gia các chương trình tình nguyện?")}
                  >
                    🤝 Chương trình tình nguyện
                  </Button>
                  <Button
                    variant="outline"
                    className="text-left justify-start h-auto p-3 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-50"
                    onClick={() => setInput("Điều kiện để trở thành cán bộ Đoàn?")}
                  >
                    👥 Cán bộ Đoàn
                  </Button>
                  <Button
                    variant="outline"
                    className="text-left justify-start h-auto p-3 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-50"
                    onClick={() => setInput("Các hoạt động văn hóa, thể thao cho thanh niên?")}
                  >
                    🎯 Hoạt động văn hóa thể thao
                  </Button>
                </div>
              </div>
            )}

            {/* Quick suggestion buttons for ongoing conversations */}
            {messages.length > 0 && (
              <div className="mb-2 flex flex-wrap gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="text-xs text-gray-600 hover:text-gray-800 hover:bg-gray-50"
                  onClick={() => setInput("Báo cáo hoạt động của ")}
                >
                  📊 Báo cáo hoạt động
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="text-xs text-gray-600 hover:text-gray-800 hover:bg-gray-50"
                  onClick={() => setInput("Lịch sinh hoạt tháng này của ")}
                >
                  📅 Lịch sinh hoạt
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="text-xs text-gray-600 hover:text-gray-800 hover:bg-gray-50"
                  onClick={() => setInput("Tạo mẫu đơn ")}
                >
                  📝 Mẫu đơn
                </Button>
              </div>
            )}

            <div className="mt-2 flex items-center space-x-2 bg-white p-3 rounded-lg border border-gray-300 shadow-sm">
              <Input
                ref={inputRef}
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyPress={(e) => e.key === "Enter" && handleSendMessage()}
                placeholder="Nhập câu hỏi về MTTQ..."
                disabled={isLoading}
                className="mttq-input flex-1 border-0 focus:ring-0"
              />
              {isLoading ? (
                <Button
                  onClick={handleCancelRequest}
                  variant="outline"
                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  <X className="w-4 h-4" />
                </Button>
              ) : null}
              <Button
                onClick={handleSendMessage}
                disabled={isLoading}
                className={`transition-all ${isLoading ? 'bg-gray-400' : 'mttq-button-primary'}`}
              >
                {isLoading ? <Loader2 className="w-4 h-4 animate-spin" /> : <Send className="w-4 h-4" />}
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
