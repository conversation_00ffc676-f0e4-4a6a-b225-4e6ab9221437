"use client";
import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  Book,
  HelpCircle,
  Info,
  Settings,
  MessageCircle,
  Home,
  Newspaper,
} from "lucide-react";
import { cn } from "@/lib/utils";

import {
  Sidebar,
  Sidebar<PERSON>ontent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
} from "@/components/ui/sidebar";

export function AppSidebar() {
  const pathname = usePathname();

  const menuItems = [
    { title: "Trang chủ", icon: Home, href: "/" },
    { title: "Điểm tin", icon: Newspaper, href: "/news" },
    { title: "Hướng dẫn", icon: Book, href: "/getting-started" },
    { title: "Cách hoạt động", icon: HelpCircle, href: "/how-it-works" },
    { title: "FAQ", icon: MessageCircle, href: "/faq" },
  ];

  return (
    <Sidebar>
      <SidebarHeader className="border-b border-white/20 p-4 mttq-header">
        <h2 className="text-lg font-semibold text-white">Menu MTTQ</h2>
      </SidebarHeader>
      <SidebarContent>
        <SidebarMenu>
          {menuItems.map((item) => (
            <SidebarMenuItem key={item.title}>
              <Link
                href={item.href}
                className={cn(
                  "flex items-center py-3 px-4 text-sm font-medium rounded-md transition-colors",
                  pathname === item.href
                    ? "bg-white/20 text-white font-semibold"
                    : "text-white/80 hover:bg-white/10 hover:text-white"
                )}
              >
                <item.icon className="mr-3 h-5 w-5" />
                <span>{item.title}</span>
              </Link>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarContent>
      <SidebarFooter className="border-t border-white/20 p-4">
        <p className="text-sm text-white/70">
          2025 Trường Đại học Công nghiệp Hà Nội
        </p>
      </SidebarFooter>
    </Sidebar>
  );
}
