"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { MessageCircle } from "lucide-react";

const navItems = [
  { href: "/", label: "Trang chủ" },
  { href: "/news", label: "Điểm tin" },
  { href: "/getting-started", label: "Hướng dẫn" },
  { href: "/how-it-works", label: "Cách hoạt động" },
  { href: "/faq", label: "FAQ" },
];

export function Header() {
  const pathname = usePathname();

  return (
    <header className="sticky top-0 z-50 w-full border-b mttq-header shadow-sm">
      <div className="container flex h-16 items-center justify-center space-x-8">
        <div className="flex items-center gap-6">
          <Link href="/" className="flex items-center space-x-2 group">
            <div className="flex items-center justify-center w-11 h-11 rounded-xl mttq-bg-primary shadow-md group-hover:shadow-lg transition-all duration-300 transform group-hover:scale-105">
              <span className="text-xl text-white">🤖</span>
            </div>
            <span className="hidden sm:inline-block text-xl font-bold text-white group-hover:opacity-80 transition-opacity">
              Chatbot MTTQ
            </span>
          </Link>
          <nav className="hidden md:flex items-center space-x-8 text-sm font-medium">
            {navItems.map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className={`relative transition-colors px-3 py-2 rounded-md
                  ${
                    pathname === item.href
                      ? "text-white font-bold bg-white/20"
                      : "text-white/80 hover:text-white hover:bg-white/10"
                  }
                `}
              >
                {item.label}
              </Link>
            ))}
          </nav>
        </div>
        <div className="flex items-center gap-4">
          <Link href="/chat">
            <Button
              size="sm"
              className="mttq-button-primary flex items-center gap-2"
            >
              <MessageCircle className="w-4 h-4" />
              Bắt đầu hỏi đáp
            </Button>
          </Link>
        </div>
      </div>
    </header>
  );
}
