"use client";

import Image from "next/image";
import Link from "next/link";
import { Header } from "@/components/shared/header";
import { Footer } from "@/components/shared/footer";

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-white">
      <Header />

      {/* Hero Section */}
      <section className="mttq-section-header">
        <div className="max-w-7xl mx-auto px-6 lg:px-8 py-8">
          <div className="flex items-center space-x-4">
            <Image
              src="/Logo-MTTQ.png"
              alt="Logo MTTQ"
              width={80}
              height={80}
              className="rounded-full"
            />
            <div>
              <h1 className="text-3xl font-bold text-white">
                Giới thiệu về hệ thống
              </h1>
              <p className="text-white/90 text-lg">
                Hệ thống hỗ trợ báo cáo và khai báo thông tin MTTQ
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-6 lg:px-8 py-12">
        <div className="mttq-form-section">
          <div className="mttq-section-header rounded-t-lg">
            <h2 className="text-xl font-semibold">Giới thiệu Chatbot MTTQ</h2>
          </div>
          <div className="p-6">
            <p className="text-gray-700 mb-6">
            Chatbot MTTQ là trợ lý ảo trực tuyến giúp bạn nhanh chóng tìm
            kiếm thông tin về các hoạt động, sự kiện và chương trình của Mặt trận tổ quốc. Thay vì mất thời gian tìm kiếm thủ công,
            bạn chỉ cần trò chuyện trực tiếp với Chatbot và nhận câu trả lời ngay
            lập tức.
          </p>
            <h3 className="text-lg font-semibold text-gray-800 mb-3">
              Chatbot hoạt động như thế nào?
            </h3>
            <p className="text-gray-700 mb-4">
            Bạn có thể hình dung Chatbot giống như một “thư ký” luôn sẵn sàng hỗ
            trợ:
          </p>
          <ul className="list-disc pl-6 text-gray-600 mb-6 space-y-2">
            <li>
              <strong>Hiểu câu hỏi của bạn</strong> – Khi bạn nhập câu hỏi, Chatbot
              sẽ “đọc” và phân tích nội dung bằng công nghệ hiểu ngôn ngữ tự
              nhiên (giống như việc con người nghe và hiểu lời nói).
            </li>
            <li>
              <strong>Tìm thông tin phù hợp</strong> – Chatbot tra cứu dữ liệu từ
              kho thông tin đã được chuẩn bị sẵn, bao gồm tài liệu chính thức, lịch
              sự kiện, hướng dẫn tham gia, và câu trả lời cho các thắc mắc thường
              gặp.
            </li>
            <li>
              <strong>Tổng hợp và trả lời</strong> – Thông tin sẽ được sắp xếp ngắn
              gọn, dễ hiểu, trình bày như khi bạn trò chuyện với một cán bộ Đoàn –
              Hội.
            </li>
          </ul>
          <h2 className="text-2xl font-semibold text-gray-800 mb-2">
            Bạn có thể hỏi Chatbot về:
          </h2>
          <ul className="list-disc pl-6 text-gray-600 mb-6 space-y-2">
            <li>
              📅 Lịch sự kiện: Thời gian, địa điểm, nội dung hoạt động Đoàn – Hội.
            </li>
            <li>
              📝 Hướng dẫn tham gia: Quy trình đăng ký các chương trình, cuộc thi,
              hoặc chiến dịch tình nguyện.
            </li>
            <li>
              📚 Tài liệu hỗ trợ: Mẫu đơn, quy định, hướng dẫn tổ chức hoạt động.
            </li>
            <li>
              ❓ Giải đáp thắc mắc: Các câu hỏi thường gặp liên quan đến tổ chức,
              quyền lợi và nghĩa vụ.
            </li>
          </ul>
          <h2 className="text-2xl font-semibold text-gray-800 mb-2">
            Cách sử dụng Chatbot hiệu quả
          </h2>
          <ul className="list-disc pl-6 text-gray-600 mb-6 space-y-2">
            <li>
              Hỏi rõ ràng, cụ thể: Ví dụ “Lịch sinh hoạt Chi đoàn tháng 8” thay vì
              chỉ hỏi “Lịch tháng này”.
            </li>
            <li>
              Dùng từ khóa liên quan: Nếu muốn tìm tài liệu, có thể nhập “Mẫu báo
              cáo hoạt động tháng”.
            </li>
            <li>
              Trao đổi nhiều lượt: Nếu chưa nhận được đúng thông tin, bạn có thể
              hỏi lại hoặc yêu cầu Chatbot làm rõ.
            </li>
          </ul>
          <h2 className="text-2xl font-semibold text-gray-800 mb-2">
            Lợi ích khi sử dụng Chatbot
          </h2>
          <ul className="list-disc pl-6 text-gray-600 mb-6 space-y-2">
            <li>Tiết kiệm thời gian tìm kiếm thông tin.</li>
            <li>Luôn sẵn sàng hỗ trợ 24/7.</li>
          </ul>
            <div className="text-center mt-6">
              <Link
                href="/chat"
                className="mttq-button-primary inline-block"
              >
                Bắt đầu trò chuyện
              </Link>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}